import 'package:daleel/res/app_asset_paths.dart';
import 'package:flutter/material.dart';

class SplashWidget extends StatefulWidget {
  const SplashWidget({super.key});

  @override
  State<SplashWidget> createState() => _SplashWidgetState();
}

class _SplashWidgetState extends State<SplashWidget> {
  static const _initialDelay = Duration(milliseconds: 200);
  static const _imageSwitchDelay = Duration(seconds: 3);
  static const _fadeDuration = Duration(milliseconds: 700);

  double _firstImageOpacity = 0.0;
  double _secondImageOpacity = 0.0;

  @override
  void initState() {
    super.initState();
    _startAnimations();
  }

  void _startAnimations() async {
    await Future.delayed(_initialDelay);
    if (!mounted) return;
    setState(() => _firstImageOpacity = 1.0);

    await Future.delayed(_imageSwitchDelay);
    if (!mounted) return;
    setState(() => _firstImageOpacity = 0.0);

    await Future.delayed(_fadeDuration);
    if (!mounted) return;
    setState(() => _secondImageOpacity = 1.0);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Stack(
          alignment: Alignment.center,
          children: [
            AnimatedOpacity(
              opacity: _firstImageOpacity,
              duration: _fadeDuration,
              child: Image.asset(
                AppAssetPaths.imgSpeaSplash,
                fit: BoxFit.contain,
              ),
            ),
            AnimatedOpacity(
              opacity: _secondImageOpacity,
              duration: _fadeDuration,
              child: Image.asset(
                AppAssetPaths.imgDaleelSplash,
                fit: BoxFit.contain,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
