import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
part 'onboarding_event.dart';
part 'onboarding_state.dart';

class OnboardingBloc extends Bloc<OnboardingEvent, OnboardingState> {
  OnboardingBloc() : super(const OnboardingInitial()) {
    on<OnboardingPageChanged>(_onPageChanged);
    on<OnboardingNextPressed>(_onNextPressed);
    on<OnboardingCompleted>(_onCompleted);
    on<OnboardingVisibilityChanged>(_onVisibilityChanged);
  }

  void _onPageChanged(OnboardingPageChanged event, Emitter<OnboardingState> emit) {
    emit(OnboardingUpdatedState(
      currentPage: event.pageIndex,
      isVisible: state.isVisible,
    ));
  }

  void _onNextPressed(OnboardingNextPressed event, Emitter<OnboardingState> emit) {
    const int maxPages = 3;
    if (state.currentPage < maxPages - 1) {
      emit(OnboardingUpdatedState(
        currentPage: state.currentPage + 1,
        isVisible: state.isVisible,
      ));
    } else {
      emit(const OnboardingCompletedState());
    }
  }

  void _onCompleted(OnboardingCompleted event, Emitter<OnboardingState> emit) {
    emit(const OnboardingCompletedState());
  }

  void _onVisibilityChanged(OnboardingVisibilityChanged event, Emitter<OnboardingState> emit) {
    emit(OnboardingUpdatedState(
      currentPage: state.currentPage,
      isVisible: event.isVisible,
    ));
  }
}

