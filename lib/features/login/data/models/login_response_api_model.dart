class LoginResponseApiModel {
  final String token;
  final User user;
  final DateTime expires;

  LoginResponseApiModel({
    required this.token,
    required this.user,
    required this.expires,
  });

  factory LoginResponseApiModel.fromJson(Map<String, dynamic> json) {
    return LoginResponseApiModel(
      token: json['token'] as String,
      user: User.from<PERSON><PERSON>(json['user']),
      expires: DateTime.parse(json['expires']),
    );
  }
}

class User {
  final String scope;
  final dynamic ownerId;
  final String fullName;
  final String arabicFullName;
  final dynamic photoPath;
  final DateTime lastLoginDate;
  final DateTime createdDate;
  final String createdBy;
  final DateTime updatedDate;
  final dynamic updatedBy;
  final bool isActive;
  final bool isAdUser;
  final int id;
  final String userName;
  final String email;
  final bool emailConfirmed;
  final String phoneNumber;
  final List<Role> roles;
  final List<String> claims;

  User({
    required this.scope,
    required this.ownerId,
    required this.fullName,
    required this.arabicFullName,
    required this.photoPath,
    required this.lastLoginDate,
    required this.createdDate,
    required this.createdBy,
    required this.updatedDate,
    required this.updatedBy,
    required this.isActive,
    required this.isAdUser,
    required this.id,
    required this.userName,
    required this.email,
    required this.emailConfirmed,
    required this.phoneNumber,
    required this.roles,
    required this.claims,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      scope: json['scope'] as String,
      ownerId: json['ownerId'],
      fullName: json['fullName'] as String,
      arabicFullName: json['arabicFullName'] as String,
      photoPath: json['photoPath'],
      lastLoginDate: DateTime.parse(json['lastLoginDate']),
      createdDate: DateTime.parse(json['createdDate']),
      createdBy: json['createdBy'] as String,
      updatedDate: DateTime.parse(json['updatedDate']),
      updatedBy: json['updatedBy'],
      isActive: json['isActive'] as bool,
      isAdUser: json['isADUser'] as bool,
      id: json['id'] as int,
      userName: json['userName'] as String,
      email: json['email'] as String,
      emailConfirmed: json['emailConfirmed'] as bool,
      phoneNumber: json['phoneNumber'] as String,
      roles:
          (json['roles'] as List<dynamic>)
              .map((e) => Role.fromJson(e))
              .toList(),
      claims:
          (json['claims'] as List<dynamic>).map((e) => e.toString()).toList(),
    );
  }
}

class Role {
  final Name name;
  final List<RoleClaim> roleClaims;
  final int id;
  final String scope;
  final dynamic ownerId;
  final dynamic membersCount;
  final DateTime createdDate;
  final DateTime updatedDate;

  Role({
    required this.name,
    required this.roleClaims,
    required this.id,
    required this.scope,
    required this.ownerId,
    required this.membersCount,
    required this.createdDate,
    required this.updatedDate,
  });

  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      name: Name.fromJson(json['name']),
      roleClaims:
          (json['roleClaims'] as List<dynamic>)
              .map((e) => RoleClaim.fromJson(e))
              .toList(),
      id: json['id'] as int,
      scope: json['scope'] as String,
      ownerId: json['ownerId'],
      membersCount: json['membersCount'],
      createdDate: DateTime.parse(json['createdDate']),
      updatedDate: DateTime.parse(json['updatedDate']),
    );
  }
}

class Name {
  final String en;
  final String ar;

  Name({required this.en, required this.ar});

  factory Name.fromJson(Map<String, dynamic> json) {
    return Name(en: json['en'] as String, ar: json['ar'] as String);
  }
}

class RoleClaim {
  final dynamic title;
  final int id;
  final String type;

  RoleClaim({required this.title, required this.id, required this.type});

  factory RoleClaim.fromJson(Map<String, dynamic> json) {
    return RoleClaim(
      title: json['title'],
      id: json['id'] as int,
      type: json['type'] as String,
    );
  }
}
