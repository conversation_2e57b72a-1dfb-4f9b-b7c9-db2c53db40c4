import 'package:daleel/res/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:searchable_paginated_dropdown/searchable_paginated_dropdown.dart';

/// A reusable custom searchable dropdown widget
///
/// [T] - The type of items in the dropdown
/// [items] - List of items to display in dropdown
/// [hintText] - Placeholder text when no item is selected
/// [onChanged] - Callback when an item is selected
/// [selectedValue] - Currently selected value
/// [displayText] - Function to extract display text from item
/// [searchFilter] - Function to filter items based on search query
/// [pageSize] - Number of items per page (default: 10)
class CustomSearchableDropdown<T> extends StatelessWidget {
  final List<T> items;
  final String hintText;
  final Function(T?) onChanged;
  final T? selectedValue;
  final String Function(T) displayText;
  final bool Function(T, String) searchFilter;
  final int pageSize;
  final Color? hintTextColor;
  final Color? borderColor;
  final Color? backgroundColor;
  final Color? itemTextColor;
  final double? fontSize;
  final FontWeight? fontWeight;
  final EdgeInsets? margin;
  final BorderRadius? borderRadius;
  final Icon? trailingIcon;
  final Icon? trailingClearIcon;

  const CustomSearchableDropdown({
    super.key,
    required this.items,
    required this.hintText,
    required this.onChanged,
    required this.displayText,
    required this.searchFilter,
    this.selectedValue,
    this.pageSize = 10,
    this.hintTextColor,
    this.borderColor,
    this.backgroundColor,
    this.itemTextColor,
    this.fontSize,
    this.fontWeight,
    this.margin,
    this.borderRadius,
    this.trailingIcon,
    this.trailingClearIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(
        cardTheme: const CardThemeData(
          color: Colors.white, // Set menu background to white
        ),
      ),
      child: SearchableDropdown<T>.paginated(
        hintText: Text(
          hintText,
          style: TextStyle(
            fontSize: fontSize ?? 14,
            color: hintTextColor ?? Colors.grey.shade600,
            fontWeight: fontWeight ?? FontWeight.w400,
          ),
        ),
        margin: margin ?? const EdgeInsets.all(15),
        backgroundDecoration:
            (child) => Container(
              decoration: BoxDecoration(
                color: backgroundColor ?? AppColors.whiteIcon,
                borderRadius: borderRadius ?? BorderRadius.circular(12),
                border: Border.all(
                  color:
                      borderColor ??
                      AppColors.forgetPassTitle.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: child,
            ),
        paginatedRequest: (int page, String? searchKey) async {
          // Filter based on search key if provided
          List<T> filteredItems = items;
          if (searchKey != null && searchKey.isNotEmpty) {
            filteredItems =
                items.where((item) => searchFilter(item, searchKey)).toList();
          }

          // For local data, show all filtered items regardless of pagination
          // This is because we have all data locally, not from API
          final itemsToShow = filteredItems;
          // Convert to SearchableDropdownMenuItem
          final dropdownItems =
              itemsToShow
                  .map(
                    (item) => SearchableDropdownMenuItem<T>(
                      value: item,
                      label: displayText(item),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        child: Text(
                          displayText(item),
                          style: TextStyle(
                            fontSize: fontSize ?? 14,
                            color: itemTextColor ?? AppColors.colorSecondary,
                            fontWeight: fontWeight ?? FontWeight.w400,
                          ),
                        ),
                      ),
                    ),
                  )
                  .toList();
          return dropdownItems;
        },
        requestItemCount: items.length,
        onChanged: onChanged,
        trailingIcon:
            trailingIcon ??
            Icon(Icons.keyboard_arrow_down, color: AppColors.colorSecondary),
        trailingClearIcon:
            trailingClearIcon ??
            Icon(Icons.close, color: AppColors.appBarBackground, size: 20),
      ),
    );
  }
}

/// Extension to make it easier to use with RequestStatus
extension RequestStatusDropdown on CustomSearchableDropdown<dynamic> {
  /// Creates a dropdown specifically for RequestStatus objects
  static CustomSearchableDropdown<T> forRequestStatus<T>({
    required List<T> items,
    required String hintText,
    required Function(T?) onChanged,
    required String Function(T) getName,
    T? selectedValue,
    int pageSize = 10,
  }) {
    return CustomSearchableDropdown<T>(
      items: items,
      hintText: hintText,
      onChanged: onChanged,
      selectedValue: selectedValue,
      pageSize: pageSize,
      displayText: getName,
      searchFilter:
          (item, searchKey) =>
              getName(item).toLowerCase().contains(searchKey.toLowerCase()),
    );
  }
}
