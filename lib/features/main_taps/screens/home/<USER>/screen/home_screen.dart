import 'package:daleel/apis/_base/dio_api_manager.dart';
import 'package:daleel/core/widgets/base_stateful_screen_widget.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/bloc/home_bloc.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/datasources/home_api_manager.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repository/home_repository.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_current_school_year_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_request_enum_values_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_certificates_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_my_requests_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_guardian_by_user_id_usecase.dart';
import 'package:daleel/preferences/preferences_manager.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:daleel/utils/feedback/feedback_message.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import '../widgets/user_header.dart';
import '../widgets/home_stats_section.dart';
import '../widgets/home_buttons_section.dart';
import '../widgets/home_certificates_section.dart';
import '../widgets/home_requests_section.dart';

class HomeScreen extends StatelessWidget {
  HomeScreen({super.key});

  final PreferencesManager preferencesManager = GetIt.I<PreferencesManager>();
  final DioApiManager dioApiManager = GetIt.I<DioApiManager>();

  @override
  Widget build(BuildContext context) {
    HomeRepository homeRepository = HomeRepository(
      HomeApiManager(dioApiManager),
    );

    final getCurrentSchoolYearUseCase = GetCurrentSchoolYearUseCase(
      homeRepository,
    );
    final getRequestEnumValuesUseCase = GetRequestEnumValuesUseCase(
      homeRepository,
    );
    final getCertificatesUseCase = GetCertificatesUseCase(homeRepository);
    final getMyRequestsUseCase = GetMyRequestsUseCase(homeRepository);
    final getGuardianByUserIdUseCase = GetGuardianByUserIdUseCase(
      homeRepository,
    );

    return BlocProvider<HomeBloc>(
      create:
          (context) =>
              HomeBloc(
                  getCurrentSchoolYearUseCase: getCurrentSchoolYearUseCase,
                  getRequestEnumValuesUseCase: getRequestEnumValuesUseCase,
                  getCertificatesUseCase: getCertificatesUseCase,
                  getMyRequestsUseCase: getMyRequestsUseCase,
                  getGuardianByUserIdUseCase: getGuardianByUserIdUseCase,
                )
                ..add(LoadHomeDataEvent())
                ..add(const LoadGuardianEvent()),
      child: const HomeScreenWithBloc(),
    );
  }
}

class HomeScreenWithBloc extends BaseStatefulScreenWidget {
  const HomeScreenWithBloc({super.key});

  @override
  BaseScreenState<HomeScreenWithBloc> baseScreenCreateState() =>
      _HomeScreenWithBlocState();
}

class _HomeScreenWithBlocState extends BaseScreenState<HomeScreenWithBloc> {
  List<RequestApiModel> requests = [];

  @override
  void initState() {
    super.initState();
    // Refresh user data when entering home screen
    _refreshUserData();
  }

  Future<void> _refreshUserData() async {
    final userManager = GetIt.I<UserMangers>();
    await userManager.setUserInfoAndModeFromLocal();
  }

  void _toggleExpand(int instanceId) {
    context.read<HomeBloc>().add(
      ToggleRequestExpandEvent(instanceId: instanceId),
    );
  }

  @override
  Widget baseScreenBuild(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is HomeLoadingState) {
            showLoading();
          } else {
            hideLoading();
          }
          if (state is HomeErrorState) {
            showFeedbackMessage(state.errorMessage);
          }
        },
        builder: (context, state) => _buildHomeWidget(state),
      ),
    );
  }

  Widget _buildHomeWidget(HomeState state) {
    return SingleChildScrollView(
      child: Column(
        children: [
          const UserHeader(),
          SizedBox(height: 20.h),
          SizedBox(height: 20.h),
          HomeStatsSection(state: state),
          const HomeButtonsSection(),
          HomeCertificatesSection(
            state: state,
            onShowAllPressed: _onShowAllPressed,
          ),
          HomeRequestsSection(
            state: state,
            onShowAllPressed: _onShowAllPressed,
            onSearchPressed: _onSearchPressed,
            onResetPressed: _onResetPressed,
            onToggleExpand: _toggleExpand,
          ),
        ],
      ),
    );
  }

  HomeBloc get currentBloc => context.read<HomeBloc>();

  // Helper methods for dropdown data

  void _onShowAllPressed() {}

  void _onSearchPressed() {
    final currentState = context.read<HomeBloc>().state;
    if (currentState is HomeLoadedState) {
      // Get selected filter values from state
      final requestTypeIds =
          currentState.selectedRequestType != null
              ? [currentState.selectedRequestType!.id]
              : <int>[];
      final requestStatusIds =
          currentState.selectedRequestStatus != null
              ? [currentState.selectedRequestStatus!.id]
              : <int>[];

      // Trigger search event
      context.read<HomeBloc>().add(
        SearchRequestsEvent(
          requestStatus: requestStatusIds,
          requestType: requestTypeIds,
          keyWord: '',
        ),
      );
    }
  }

  void _onResetPressed() {
    // Trigger reset filters event (this will also reset requests)
    context.read<HomeBloc>().add(const ResetFiltersEvent());
  }
}
