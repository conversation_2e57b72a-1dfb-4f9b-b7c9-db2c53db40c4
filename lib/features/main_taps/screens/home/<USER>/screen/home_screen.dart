import 'package:daleel/apis/_base/dio_api_manager.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/core/widgets/base_stateful_screen_widget.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/bloc/home_bloc.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/datasources/home_api_manager.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_enums_value_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repository/home_repository.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_current_school_year_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_request_enum_values_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_certificates_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_my_requests_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_guardian_by_user_id_usecase.dart';
import 'package:daleel/features/widgets/app_buttons/app_elevated_button.dart';
import 'package:daleel/preferences/preferences_manager.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/feedback/feedback_message.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:daleel/features/widgets/custom_searchable_dropdown.dart';
import '../widgets/home_stat_card.dart';
import '../widgets/certificate_widget.dart';
import '../widgets/user_header.dart';
import '../widgets/home_stats_section.dart';
import '../widgets/home_buttons_section.dart';
import '../widgets/home_certificates_section.dart';
import '../widgets/home_requests_section.dart';

class HomeScreen extends StatelessWidget {
  HomeScreen({super.key});

  final PreferencesManager preferencesManager = GetIt.I<PreferencesManager>();
  final DioApiManager dioApiManager = GetIt.I<DioApiManager>();

  @override
  Widget build(BuildContext context) {
    HomeRepository homeRepository = HomeRepository(
      HomeApiManager(dioApiManager),
    );

    final getCurrentSchoolYearUseCase = GetCurrentSchoolYearUseCase(
      homeRepository,
    );
    final getRequestEnumValuesUseCase = GetRequestEnumValuesUseCase(
      homeRepository,
    );
    final getCertificatesUseCase = GetCertificatesUseCase(homeRepository);
    final getMyRequestsUseCase = GetMyRequestsUseCase(homeRepository);
    final getGuardianByUserIdUseCase = GetGuardianByUserIdUseCase(
      homeRepository,
    );

    return BlocProvider<HomeBloc>(
      create:
          (context) =>
              HomeBloc(
                  getCurrentSchoolYearUseCase: getCurrentSchoolYearUseCase,
                  getRequestEnumValuesUseCase: getRequestEnumValuesUseCase,
                  getCertificatesUseCase: getCertificatesUseCase,
                  getMyRequestsUseCase: getMyRequestsUseCase,
                  getGuardianByUserIdUseCase: getGuardianByUserIdUseCase,
                )
                ..add(LoadHomeDataEvent())
                ..add(const LoadGuardianEvent()),
      child: const HomeScreenWithBloc(),
    );
  }
}

class HomeScreenWithBloc extends BaseStatefulScreenWidget {
  const HomeScreenWithBloc({super.key});

  @override
  BaseScreenState<HomeScreenWithBloc> baseScreenCreateState() =>
      _HomeScreenWithBlocState();
}

class _HomeScreenWithBlocState extends BaseScreenState<HomeScreenWithBloc> {
  List<RequestApiModel> requests = [];

  @override
  void initState() {
    super.initState();
    // Refresh user data when entering home screen
    _refreshUserData();
  }

  Future<void> _refreshUserData() async {
    final userManager = GetIt.I<UserMangers>();
    await userManager.setUserInfoAndModeFromLocal();
  }

  void _toggleExpand(int instanceId) {
    context.read<HomeBloc>().add(
      ToggleRequestExpandEvent(instanceId: instanceId),
    );
  }

  @override
  Widget baseScreenBuild(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<HomeBloc, HomeState>(
        listener: (context, state) {
          if (state is HomeLoadingState) {
            showLoading();
          } else {
            hideLoading();
          }
          if (state is HomeErrorState) {
            showFeedbackMessage(state.errorMessage);
          }
        },
        builder: (context, state) => _buildHomeWidget(state),
      ),
    );
  }

  Widget _buildHomeWidget(HomeState state) {
    return SingleChildScrollView(
      child: Column(
        children: [
          const UserHeader(),
          SizedBox(height: 20.h),
          SizedBox(height: 20.h),
          HomeStatsSection(state: state),
          const HomeButtonsSection(),
          HomeCertificatesSection(
            state: state,
            onShowAllPressed: _onShowAllPressed,
          ),
          HomeRequestsSection(
            state: state,
            onShowAllPressed: _onShowAllPressed,
            onSearchPressed: _onSearchPressed,
            onResetPressed: _onResetPressed,
            onToggleExpand: _toggleExpand,
          ),
        ],
      ),
    );
  }

  Widget _buildButtonsSection() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          SizedBox(
            height: 50.h,
            width: double.infinity,
            child: AppElevatedButton.withTitle(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              onPressed: _onAddNewSonPressed,
              title: context.translate(LocalizationKeys.addNewSon),
              fontWeight: FontWeight.w400,
              fontSize: 14,
            ),
          ),
          SizedBox(height: 20.h),
          SizedBox(
            height: 50.h,
            width: double.infinity,
            child: AppElevatedButton.withTitle(
              shape: RoundedRectangleBorder(
                side: BorderSide(color: AppColors.colorSecondary, width: 1.5),
                borderRadius: BorderRadius.circular(16),
              ),
              onPressed: _onDelegationPressed,
              color: Colors.transparent,
              textColor: AppColors.colorSecondary,
              title: context.translate(LocalizationKeys.delegation),
              fontWeight: FontWeight.w400,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCertificatesSection(HomeState state) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Text(
                context.translate(LocalizationKeys.certificates),
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 16,
                  color: AppColors.colorSecondary,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: _onShowAllPressed,
                child: Container(
                  width: 100,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: AppColors.colorshowAll.withValues(alpha: 0.2),
                  ),
                  child: Center(
                    child: Text(
                      context.translate(LocalizationKeys.showAll),
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                        color: AppColors.colorSecondary,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 10.h),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: _buildCertificatesList(state),
        ),
      ],
    );
  }

  Widget _buildCertificatesList(HomeState state) {
    if (state is HomeLoadedState) {
      if (state.certificates.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Text(
            "No certificates found",
            style: TextStyle(fontSize: 14, color: AppColors.colorshowAll),
            textAlign: TextAlign.center,
          ),
        );
      }

      return Column(
        children:
            state.certificates.map((certificate) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: CertificateWidget(certificate: certificate),
              );
            }).toList(),
      );
    }

    return Column(
      children: List.generate(
        4,
        (index) => Padding(
          padding: const EdgeInsets.only(bottom: 20),
          child: const CertificateWidget(),
        ),
      ),
    );
  }

  Widget _buildRequestsSection(HomeState state) {
    final requests =
        state is HomeLoadedState ? state.requests : <RequestApiModel>[];
    final isRequestsLoading =
        state is HomeLoadedState ? state.isRequestsLoading : true;

    return Column(
      children: [
        // Header with title and show all
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Text(
                context.translate(LocalizationKeys.requests),
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 16,
                  color: AppColors.colorSecondary,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: _onShowAllPressed,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: AppColors.colorshowAll.withValues(alpha: 0.2),
                  ),
                  child: Text(
                    context.translate(LocalizationKeys.showAll),
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                      color: AppColors.colorSecondary,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16.h),

        // Filters section
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              // Request type dropdown - Using Custom Widget with BlocBuilder
              Expanded(
                child: BlocBuilder<HomeBloc, HomeState>(
                  builder: (context, state) {
                    final requestTypes = _getRequestTypes(state);

                    // Show loading or disabled state if no data
                    if (requestTypes.isEmpty) {
                      return Container(
                        height: 50,
                        margin: const EdgeInsets.all(15),
                        decoration: BoxDecoration(
                          color: AppColors.whiteIcon,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: AppColors.forgetPassTitle.withValues(
                              alpha: 0.3,
                            ),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            context.translate(LocalizationKeys.requestType),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      );
                    }

                    return CustomSearchableDropdown<RequestStatus>(
                      items: requestTypes,
                      hintText: context.translate(LocalizationKeys.requestType),
                      selectedValue:
                          state is HomeLoadedState
                              ? state.selectedRequestType
                              : null,
                      displayText: (RequestStatus item) => item.name,
                      searchFilter:
                          (RequestStatus item, String searchKey) => item.name
                              .toLowerCase()
                              .contains(searchKey.toLowerCase()),
                      onChanged: (RequestStatus? value) {
                        context.read<HomeBloc>().add(
                          UpdateRequestTypeSelectionEvent(value),
                        );
                      },
                      hintTextColor: AppColors.forgetPassTitle,
                    );
                  },
                ),
              ),
              SizedBox(width: 12.w),

              // Request status dropdown - Using Custom Widget with BlocBuilder
              Expanded(
                child: BlocBuilder<HomeBloc, HomeState>(
                  builder: (context, state) {
                    final requestStatuses = _getRequestStatuses(state);

                    // Show loading or disabled state if no data
                    if (requestStatuses.isEmpty) {
                      return Container(
                        height: 50,
                        margin: const EdgeInsets.all(15),
                        decoration: BoxDecoration(
                          color: AppColors.whiteIcon,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: AppColors.forgetPassTitle.withValues(
                              alpha: 0.3,
                            ),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            context.translate(LocalizationKeys.requestStatus),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      );
                    }

                    return CustomSearchableDropdown<RequestStatus>(
                      items: requestStatuses,
                      hintText: context.translate(
                        LocalizationKeys.requestStatus,
                      ),
                      selectedValue:
                          state is HomeLoadedState
                              ? state.selectedRequestStatus
                              : null,
                      displayText: (RequestStatus item) => item.name,
                      searchFilter:
                          (RequestStatus item, String searchKey) => item.name
                              .toLowerCase()
                              .contains(searchKey.toLowerCase()),
                      onChanged: (RequestStatus? value) {
                        context.read<HomeBloc>().add(
                          UpdateRequestStatusSelectionEvent(value),
                        );
                      },
                      hintTextColor: AppColors.forgetPassTitle,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16.h),

        // Action buttons
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              // Search button
              Expanded(
                child: GestureDetector(
                  onTap: _onSearchPressed,
                  child: Container(
                    height: 48.h,
                    decoration: BoxDecoration(
                      color: AppColors.colorSecondary,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: Text(
                        context.translate(LocalizationKeys.search),
                        style: TextStyle(
                          color: AppColors.whiteIcon,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12.w),

              // Reset button
              Expanded(
                child: GestureDetector(
                  onTap: _onResetPressed,
                  child: Container(
                    height: 48.h,
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.colorSecondary),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: Text(
                        context.translate(LocalizationKeys.reset),
                        style: TextStyle(
                          color: AppColors.colorSecondary,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 8.h),

        // Request cards
        isRequestsLoading
            ? Container(
              padding: EdgeInsets.all(20.w),
              child: Center(
                child: CircularProgressIndicator(
                  color: AppColors.colorSecondary,
                ),
              ),
            )
            : requests.isEmpty
            ? Container(
              padding: EdgeInsets.all(20.w),
              child: Center(
                child: Text(
                  "No Requests Found",
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.forgetPassTitle,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            )
            : ListView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemCount: requests.length,
              itemBuilder: (context, index) {
                final request = requests[index];
                return _buildRequestItem(request);
              },
            ),
      ],
    );
  }

  Widget _buildRequestItem(RequestApiModel request) {
    return Card(
      color: AppColors.colorCertificateBackground,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: InkWell(
        onTap: () => _toggleExpand(request.instanceId),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header (always visible)
              Row(
                children: [
                  Image.asset(
                    AppAssetPaths.ic_request_icon,
                    width: 24,
                    height: 24,
                  ),
                  SizedBox(width: 20.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          request.requestType,
                          style: const TextStyle(
                            color: AppColors.appBarBackground,
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    request.isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: AppColors.appBarBackground,
                  ),
                ],
              ),

              // Expandable details
              if (request.isExpanded) ...[
                const Divider(height: 24),
                _buildDetailRow(
                  'Request Number',
                  request.requestNumber,
                  false,
                  true,
                ),
                _buildDetailRow(
                  'Request related to',
                  request.relatedSon.name.en,
                  false,
                  false,
                ),
                _buildDetailRow('School', request.school.name.en, false, false),
                _buildDetailRow(
                  'Curriculum',
                  request.curriculum.name.en,
                  false,
                  false,
                ),
                _buildDetailRow(
                  'Created by',
                  request.createdBy.en,
                  false,
                  false,
                ),
                _buildDetailRow(
                  'Request Date',
                  _formatDate(request.createdDate),
                  false,
                  false,
                ),
                _buildDetailRow(
                  'Modification Date',
                  request.lastUpdatedDate != null
                      ? _formatDate(request.lastUpdatedDate!)
                      : 'N/A',
                  true,
                  false,
                ),
                SizedBox(height: 8.h),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value,
    bool isDetails,
    bool isStatus,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 12,
                color: AppColors.mainGray,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 12,
                color: AppColors.mainGray,
              ),
            ),
          ),
          isStatus
              ? Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: AppColors.blueGradient.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  context.translate(LocalizationKeys.pending),
                  style: TextStyle(fontSize: 12, color: AppColors.statusHome),
                ),
              )
              : const SizedBox.shrink(),
          isDetails
              ? Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.colorSecondary),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  context.translate(LocalizationKeys.details),
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    color: AppColors.colorSecondary,
                    fontSize: 12,
                  ),
                ),
              )
              : const SizedBox.shrink(),
        ],
      ),
    );
  }

  HomeBloc get currentBloc => context.read<HomeBloc>();

  // Helper methods for dropdown data
  List<RequestStatus> _getRequestTypes(HomeState state) {
    if (state is HomeLoadedState && state.requestEnumValues != null) {
      final types = state.requestEnumValues!.requestTypes;
      return types;
    }
    return [];
  }

  List<RequestStatus> _getRequestStatuses(HomeState state) {
    if (state is HomeLoadedState && state.requestEnumValues != null) {
      final statuses = state.requestEnumValues!.requestStatuses;
      return statuses;
    }
    return [];
  }

  void _onAddNewSonPressed() {
    context.push('/add-son-selection');
  }

  void _onDelegationPressed() {}

  void _onShowAllPressed() {}

  void _onSearchPressed() {
    final currentState = context.read<HomeBloc>().state;
    if (currentState is HomeLoadedState) {
      // Get selected filter values from state
      final requestTypeIds =
          currentState.selectedRequestType != null
              ? [currentState.selectedRequestType!.id]
              : <int>[];
      final requestStatusIds =
          currentState.selectedRequestStatus != null
              ? [currentState.selectedRequestStatus!.id]
              : <int>[];

      // Trigger search event
      context.read<HomeBloc>().add(
        SearchRequestsEvent(
          requestStatus: requestStatusIds,
          requestType: requestTypeIds,
          keyWord: '',
        ),
      );
    }
  }

  void _onResetPressed() {
    // Trigger reset filters event (this will also reset requests)
    context.read<HomeBloc>().add(const ResetFiltersEvent());
  }

  String _formatDate(String dateString) {
    try {
      final DateTime date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }
}
