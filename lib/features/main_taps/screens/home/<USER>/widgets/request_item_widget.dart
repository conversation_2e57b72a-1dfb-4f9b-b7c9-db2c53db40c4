import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_api_model.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RequestItemWidget extends StatelessWidget {
  final RequestApiModel request;
  final VoidCallback onTap;

  const RequestItemWidget({
    super.key,
    required this.request,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: AppColors.colorCertificateBackground,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header (always visible)
              Row(
                children: [
                  Image.asset(
                    AppAssetPaths.ic_request_icon,
                    width: 24,
                    height: 24,
                  ),
                  SizedBox(width: 20.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          request.requestType,
                          style: const TextStyle(
                            color: AppColors.appBarBackground,
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    request.isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: AppColors.appBarBackground,
                  ),
                ],
              ),

              // Expandable details
              if (request.isExpanded) ...[
                const Divider(height: 24),
                _buildDetailRow(
                  'Request Number',
                  request.requestNumber,
                  false,
                  true,
                  context,
                ),
                _buildDetailRow(
                  'Request related to',
                  request.relatedSon.name.en,
                  false,
                  false,
                  context,
                ),
                _buildDetailRow(
                  'School',
                  request.school.name.en,
                  false,
                  false,
                  context,
                ),
                _buildDetailRow(
                  'Curriculum',
                  request.curriculum.name.en,
                  false,
                  false,
                  context,
                ),
                _buildDetailRow(
                  'Created by',
                  request.createdBy.en,
                  false,
                  false,
                  context,
                ),
                _buildDetailRow(
                  'Request Date',
                  _formatDate(request.createdDate),
                  false,
                  false,
                  context,
                ),
                _buildDetailRow(
                  'Modification Date',
                  request.lastUpdatedDate != null
                      ? _formatDate(request.lastUpdatedDate!)
                      : 'N/A',
                  true,
                  false,
                  context,
                ),
                SizedBox(height: 8.h),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value,
    bool isDetails,
    bool isStatus,
    BuildContext context,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 12,
                color: AppColors.mainGray,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 12,
                color: AppColors.mainGray,
              ),
            ),
          ),
          isStatus
              ? Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: AppColors.blueGradient.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  context.translate(LocalizationKeys.pending),
                  style: TextStyle(fontSize: 12, color: AppColors.statusHome),
                ),
              )
              : const SizedBox.shrink(),
          isDetails
              ? Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.colorSecondary),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  context.translate(LocalizationKeys.details),
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    color: AppColors.colorSecondary,
                    fontSize: 12,
                  ),
                ),
              )
              : const SizedBox.shrink(),
        ],
      ),
    );
  }

  String _formatDate(String dateString) {
    try {
      final DateTime date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }
}
