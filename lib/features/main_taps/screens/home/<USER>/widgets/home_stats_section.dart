import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/bloc/home_bloc.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/widgets/home_stat_card.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HomeStatsSection extends StatelessWidget {
  final HomeState state;

  const HomeStatsSection({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    if (state is HomeLoadedState) {
      return Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                HomeStatCard(
                  color: AppColors.colorHomeGreenButton,
                  text: context.translate(LocalizationKeys.registeredChildren),
                  imagePath: AppAssetPaths.ic_Open_enrollment,
                  textColor: AppColors.whiteIcon,
                ),
                SizedBox(width: 20.w),
                HomeStatCard(
                  color: AppColors.colorHomeButtons.withValues(alpha: 0.2),
                  text: context.translate(
                    LocalizationKeys.unregisteredChildren,
                  ),
                  textColor: AppColors.colorHomeButtons,
                  imagePath: AppAssetPaths.ic_No_id_card,
                ),
              ],
            ),
          ),
          SizedBox(height: 20.h),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                HomeStatCard(
                  color: AppColors.colorHomeButtons.withValues(alpha: 0.2),
                  text: context.translate(LocalizationKeys.sonsWithdrawn),
                  textColor: AppColors.colorHomeButtons,
                  imagePath: AppAssetPaths.ic_Unsubscribe,
                ),
                SizedBox(width: 20.w),
                HomeStatCard(
                  color: AppColors.colorHomeButtons.withValues(alpha: 0.2),
                  text: context.translate(
                    LocalizationKeys.sonsFinishedStudying,
                  ),
                  textColor: AppColors.colorHomeButtons,
                  imagePath: AppAssetPaths.ic_finish,
                ),
              ],
            ),
          ),
        ],
      );
    }
    return const SizedBox.shrink();
  }
}
