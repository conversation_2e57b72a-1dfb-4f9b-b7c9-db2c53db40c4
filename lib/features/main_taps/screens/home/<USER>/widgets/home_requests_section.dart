import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_enums_value_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/bloc/home_bloc.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/widgets/request_item_widget.dart';
import 'package:daleel/features/widgets/custom_searchable_dropdown.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HomeRequestsSection extends StatelessWidget {
  final HomeState state;
  final VoidCallback onShowAllPressed;
  final VoidCallback onSearchPressed;
  final VoidCallback onResetPressed;
  final Function(int) onToggleExpand;

  const HomeRequestsSection({
    super.key,
    required this.state,
    required this.onShowAllPressed,
    required this.onSearchPressed,
    required this.onResetPressed,
    required this.onToggleExpand,
  });

  @override
  Widget build(BuildContext context) {
    final requests = state is HomeLoadedState ? state.requests : <RequestApiModel>[];
    final isRequestsLoading = state is HomeLoadedState ? state.isRequestsLoading : true;

    return Column(
      children: [
        // Header with title and show all
        _buildHeader(context),
        SizedBox(height: 16.h),

        // Filters section
        _buildFiltersSection(context),
        SizedBox(height: 16.h),

        // Action buttons
        _buildActionButtons(context),
        SizedBox(height: 8.h),

        // Request cards
        _buildRequestsList(requests, isRequestsLoading, context),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Text(
            context.translate(LocalizationKeys.requests),
            style: TextStyle(
              fontWeight: FontWeight.w700,
              fontSize: 16,
              color: AppColors.colorSecondary,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: onShowAllPressed,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                color: AppColors.colorshowAll.withValues(alpha: 0.2),
              ),
              child: Text(
                context.translate(LocalizationKeys.showAll),
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: AppColors.colorSecondary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          // Request type dropdown
          Expanded(
            child: BlocBuilder<HomeBloc, HomeState>(
              builder: (context, state) {
                final requestTypes = _getRequestTypes(state);

                if (requestTypes.isEmpty) {
                  return _buildDisabledDropdown(
                    context,
                    LocalizationKeys.requestType,
                  );
                }

                return CustomSearchableDropdown<RequestStatus>(
                  items: requestTypes,
                  hintText: context.translate(LocalizationKeys.requestType),
                  selectedValue: state is HomeLoadedState ? state.selectedRequestType : null,
                  displayText: (RequestStatus item) => item.name,
                  searchFilter: (RequestStatus item, String searchKey) =>
                      item.name.toLowerCase().contains(searchKey.toLowerCase()),
                  onChanged: (RequestStatus? value) {
                    context.read<HomeBloc>().add(
                      UpdateRequestTypeSelectionEvent(value),
                    );
                  },
                  hintTextColor: AppColors.forgetPassTitle,
                );
              },
            ),
          ),
          SizedBox(width: 12.w),

          // Request status dropdown
          Expanded(
            child: BlocBuilder<HomeBloc, HomeState>(
              builder: (context, state) {
                final requestStatuses = _getRequestStatuses(state);

                if (requestStatuses.isEmpty) {
                  return _buildDisabledDropdown(
                    context,
                    LocalizationKeys.requestStatus,
                  );
                }

                return CustomSearchableDropdown<RequestStatus>(
                  items: requestStatuses,
                  hintText: context.translate(LocalizationKeys.requestStatus),
                  selectedValue: state is HomeLoadedState ? state.selectedRequestStatus : null,
                  displayText: (RequestStatus item) => item.name,
                  searchFilter: (RequestStatus item, String searchKey) =>
                      item.name.toLowerCase().contains(searchKey.toLowerCase()),
                  onChanged: (RequestStatus? value) {
                    context.read<HomeBloc>().add(
                      UpdateRequestStatusSelectionEvent(value),
                    );
                  },
                  hintTextColor: AppColors.forgetPassTitle,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDisabledDropdown(BuildContext context, String localizationKey) {
    return Container(
      height: 50,
      margin: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: AppColors.whiteIcon,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.forgetPassTitle.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          context.translate(localizationKey),
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          // Search button
          Expanded(
            child: GestureDetector(
              onTap: onSearchPressed,
              child: Container(
                height: 48.h,
                decoration: BoxDecoration(
                  color: AppColors.colorSecondary,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: Text(
                    context.translate(LocalizationKeys.search),
                    style: TextStyle(
                      color: AppColors.whiteIcon,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 12.w),

          // Reset button
          Expanded(
            child: GestureDetector(
              onTap: onResetPressed,
              child: Container(
                height: 48.h,
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.colorSecondary),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: Text(
                    context.translate(LocalizationKeys.reset),
                    style: TextStyle(
                      color: AppColors.colorSecondary,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequestsList(
    List<RequestApiModel> requests,
    bool isRequestsLoading,
    BuildContext context,
  ) {
    if (isRequestsLoading) {
      return Container(
        padding: EdgeInsets.all(20.w),
        child: Center(
          child: CircularProgressIndicator(
            color: AppColors.colorSecondary,
          ),
        ),
      );
    }

    if (requests.isEmpty) {
      return Container(
        padding: EdgeInsets.all(20.w),
        child: Center(
          child: Text(
            "No Requests Found",
            style: TextStyle(
              fontSize: 14,
              color: AppColors.forgetPassTitle,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemCount: requests.length,
      itemBuilder: (context, index) {
        final request = requests[index];
        return RequestItemWidget(
          request: request,
          onTap: () => onToggleExpand(request.instanceId),
        );
      },
    );
  }

  List<RequestStatus> _getRequestTypes(HomeState state) {
    if (state is HomeLoadedState && state.requestEnumValues != null) {
      return state.requestEnumValues!.requestTypes;
    }
    return [];
  }

  List<RequestStatus> _getRequestStatuses(HomeState state) {
    if (state is HomeLoadedState && state.requestEnumValues != null) {
      return state.requestEnumValues!.requestStatuses;
    }
    return [];
  }
}
