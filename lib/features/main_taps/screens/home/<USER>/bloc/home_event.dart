part of 'home_bloc.dart';

abstract class HomeEvent extends Equatable {
  const HomeEvent();

  @override
  List<Object> get props => [];
}

class LoadHomeDataEvent extends HomeEvent {}

class LoadRequestsEvent extends HomeEvent {
  final List<int>? requestStatus;
  final List<int>? requestType;
  final String? keyWord;
  final int page;
  final int pageSize;

  const LoadRequestsEvent({
    this.requestStatus,
    this.requestType,
    this.keyWord,
    this.page = 1,
    this.pageSize = 5,
  });

  @override
  List<Object> get props => [
    requestStatus ?? [],
    requestType ?? [],
    keyWord ?? '',
    page,
    pageSize,
  ];
}

class SearchRequestsEvent extends HomeEvent {
  final List<int> requestStatus;
  final List<int> requestType;
  final String keyWord;

  const SearchRequestsEvent({
    required this.requestStatus,
    required this.requestType,
    required this.keyWord,
  });

  @override
  List<Object> get props => [requestStatus, requestType, keyWord];
}

class ResetRequestsEvent extends HomeEvent {}

class ToggleRequestExpandEvent extends HomeEvent {
  final int instanceId;

  const ToggleRequestExpandEvent({required this.instanceId});

  @override
  List<Object> get props => [instanceId];
}

class LoadGuardianEvent extends HomeEvent {
  const LoadGuardianEvent();
}

class UpdateRequestTypeSelectionEvent extends HomeEvent {
  final RequestStatus? selectedRequestType;

  const UpdateRequestTypeSelectionEvent(this.selectedRequestType);

  @override
  List<Object> get props => [selectedRequestType ?? 'null'];
}

class UpdateRequestStatusSelectionEvent extends HomeEvent {
  final RequestStatus? selectedRequestStatus;

  const UpdateRequestStatusSelectionEvent(this.selectedRequestStatus);

  @override
  List<Object> get props => [selectedRequestStatus ?? 'null'];
}

class ResetFiltersEvent extends HomeEvent {
  const ResetFiltersEvent();
}
