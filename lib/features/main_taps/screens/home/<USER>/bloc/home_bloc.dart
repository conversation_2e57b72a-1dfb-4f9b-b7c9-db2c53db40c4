import 'package:daleel/apis/api_keys.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/current_school_year_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_enums_value_api_model.dart';

import 'package:daleel/features/main_taps/screens/home/<USER>/models/guardian_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/certificate_ui_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_ui_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_current_school_year_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_request_enum_values_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_certificates_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_my_requests_usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/usecases/get_guardian_by_user_id_usecase.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final GetRequestEnumValuesUseCase getRequestEnumValuesUseCase;
  final GetCurrentSchoolYearUseCase getCurrentSchoolYearUseCase;
  final GetCertificatesUseCase getCertificatesUseCase;
  final GetMyRequestsUseCase getMyRequestsUseCase;
  final GetGuardianByUserIdUseCase getGuardianByUserIdUseCase;

  HomeBloc({
    required this.getRequestEnumValuesUseCase,
    required this.getCurrentSchoolYearUseCase,
    required this.getCertificatesUseCase,
    required this.getMyRequestsUseCase,
    required this.getGuardianByUserIdUseCase,
  }) : super(HomeInitialState()) {
    on<LoadHomeDataEvent>(_onLoadHomeData);
    on<LoadRequestsEvent>(_onLoadRequests);
    on<SearchRequestsEvent>(_onSearchRequests);
    on<ResetRequestsEvent>(_onResetRequests);
    on<ToggleRequestExpandEvent>(_onToggleRequestExpand);
    on<LoadGuardianEvent>(_onLoadGuardian);
    on<UpdateRequestTypeSelectionEvent>(_onUpdateRequestTypeSelection);
    on<UpdateRequestStatusSelectionEvent>(_onUpdateRequestStatusSelection);
    on<ResetFiltersEvent>(_onResetFilters);
  }

  Future<void> _onLoadHomeData(
    LoadHomeDataEvent event,
    Emitter<HomeState> emit,
  ) async {
    emit(HomeLoadingState());

    RequestEnumValuesApiModel? enumValues;
    CurrentSchoolYearApiModel? schoolYear;
    List<CertificateUiModel> certificates = [];

    try {
      try {
        final enumValuesResult = await getRequestEnumValuesUseCase(NoParams());
        enumValuesResult.fold(
          (error) {
            // Don't emit error, just continue with null enumValues
            enumValues = null;
          },
          (data) {
            enumValues = data;
          },
        );
      } catch (e) {
        // Don't emit error, just continue with null enumValues
        enumValues = null;
      }

      try {
        final schoolYearResult = await getCurrentSchoolYearUseCase(NoParams());
        schoolYearResult.fold(
          (error) {
            HomeErrorState('Currrent School Year API error: $error', true);
          },
          (data) {
            schoolYear = data;
          },
        );
      } catch (e) {
        HomeErrorState(LocalizationKeys.somethingWentWrong, true);
      }

      if (schoolYear != null) {
        try {
          final certificatesResult = await getCertificatesUseCase(
            schoolYear!.id,
          );
          certificatesResult.fold(
            (error) {
              certificates = [];
            },
            (certificateListUiData) {
              certificates =
                  certificateListUiData.certificates.take(4).toList();
            },
          );
        } catch (e) {
          certificates = [];
        }
      } else {}
      emit(
        HomeLoadedState(
          requestEnumValues: enumValues,
          currentSchoolYear: schoolYear,
          certificates: certificates,
        ),
      );

      add(const LoadRequestsEvent());
    } catch (e) {
      HomeErrorState('General error in _onLoadHomeData: $e', true);
      emit(
        HomeLoadedState(
          requestEnumValues: enumValues,
          currentSchoolYear: schoolYear,
          certificates: certificates,
        ),
      );
    }
  }

  Future<void> _onLoadRequests(
    LoadRequestsEvent event,
    Emitter<HomeState> emit,
  ) async {
    if (state is HomeLoadedState) {
      final currentState = state as HomeLoadedState;

      // Show loading for requests
      emit(currentState.copyWith(isRequestsLoading: true));

      try {
        // Get current school year ID and parent ID
        final schoolYearId = currentState.currentSchoolYear?.id ?? 1038;
        final parentId =
            currentState.guardian?.id ?? 2; // Use guardian ID if available

        final searchModel = _createRequestSearchModel(
          schoolYearId: schoolYearId,
          parentId: parentId,
          requestStatus: event.requestStatus,
          requestType: event.requestType,
          keyWord: event.keyWord,
          page: event.page,
          pageSize: event.pageSize,
        );

        final result = await getMyRequestsUseCase(searchModel);

        result.fold(
          (error) {
            emit(currentState.copyWith(isRequestsLoading: false, requests: []));
          },
          (requestResponse) {
            final updatedRequests = _processRequestsWithExpandState(
              requestResponse.requests,
            );
            emit(
              currentState.copyWith(
                isRequestsLoading: false,
                requests: updatedRequests,
              ),
            );
          },
        );
      } catch (e) {
        emit(currentState.copyWith(isRequestsLoading: false, requests: []));
      }
    }
  }

  Future<void> _onSearchRequests(
    SearchRequestsEvent event,
    Emitter<HomeState> emit,
  ) async {
    // Use LoadRequestsEvent with search parameters
    add(
      LoadRequestsEvent(
        requestStatus: event.requestStatus,
        requestType: event.requestType,
        keyWord: event.keyWord,
        page: 1,
        pageSize: 5,
      ),
    );
  }

  Future<void> _onResetRequests(
    ResetRequestsEvent event,
    Emitter<HomeState> emit,
  ) async {
    // Load initial requests with default parameters
    add(const LoadRequestsEvent());
  }

  void _onToggleRequestExpand(
    ToggleRequestExpandEvent event,
    Emitter<HomeState> emit,
  ) {
    if (state is HomeLoadedState) {
      final currentState = state as HomeLoadedState;

      final updatedRequests = _toggleRequestExpansion(
        currentState.requests,
        event.instanceId,
      );

      // Update the state with new requests list
      emit(currentState.copyWith(requests: updatedRequests));
    }
  }

  Future<void> _onLoadGuardian(
    LoadGuardianEvent event,
    Emitter<HomeState> emit,
  ) async {
    final result = await getGuardianByUserIdUseCase(NoParams());

    result.fold(
      (failure) {
        // Handle error silently - guardian is optional data
      },
      (guardian) {
        if (state is HomeLoadedState) {
          final currentState = state as HomeLoadedState;
          emit(currentState.copyWith(guardian: guardian));
        }
      },
    );
  }

  // Helper Methods for cleaner code
  List<RequestUiModel> _processRequestsWithExpandState(
    List<RequestUiModel> requests,
  ) {
    return requests.asMap().entries.map((entry) {
      final index = entry.key;
      final request = entry.value;
      return request.copyWith(isExpanded: index == 0); // First item expanded
    }).toList();
  }

  List<RequestUiModel> _toggleRequestExpansion(
    List<RequestUiModel> requests,
    int targetInstanceId,
  ) {
    return requests.map((request) {
      if (request.instanceId == targetInstanceId) {
        // Toggle the current request
        return request.copyWith(isExpanded: !request.isExpanded);
      } else {
        // Close all other requests
        return request.copyWith(isExpanded: false);
      }
    }).toList();
  }

  RequestSearchModel _createRequestSearchModel({
    required int schoolYearId,
    required int parentId,
    List<int>? requestStatus,
    List<int>? requestType,
    String? keyWord,
    int page = ApiKeys.onePageValue,
    int pageSize = ApiKeys.requestsPageValue,
  }) {
    return RequestSearchModel(
      requestStatus: requestStatus ?? [],
      requestType: requestType ?? [],
      keyWord: keyWord ?? '',
      page: page,
      pageSize: pageSize,
      sortBy: ApiKeys.sortBy,
      sortColumn: '',
      sortColumnName: ApiKeys.sortColumnName,
      sortDirection: '',
      curriculumId: null,
      gradeId: null,
      userRequestType: null,
      parentId: [parentId],
      schoolYearId: schoolYearId,
    );
  }

  void _onUpdateRequestTypeSelection(
    UpdateRequestTypeSelectionEvent event,
    Emitter<HomeState> emit,
  ) {
    if (state is HomeLoadedState) {
      final currentState = state as HomeLoadedState;
      emit(
        currentState.copyWith(selectedRequestType: event.selectedRequestType),
      );
    }
  }

  void _onUpdateRequestStatusSelection(
    UpdateRequestStatusSelectionEvent event,
    Emitter<HomeState> emit,
  ) {
    if (state is HomeLoadedState) {
      final currentState = state as HomeLoadedState;
      emit(
        currentState.copyWith(
          selectedRequestStatus: event.selectedRequestStatus,
        ),
      );
    }
  }

  void _onResetFilters(ResetFiltersEvent event, Emitter<HomeState> emit) {
    if (state is HomeLoadedState) {
      final currentState = state as HomeLoadedState;
      emit(
        currentState.copyWith(
          clearSelectedRequestType: true,
          clearSelectedRequestStatus: true,
        ),
      );
      // Also trigger reset of requests
      add(ResetRequestsEvent());
    }
  }
}
