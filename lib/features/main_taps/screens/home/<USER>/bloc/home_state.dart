part of 'home_bloc.dart';

abstract class HomeState extends Equatable {
  const HomeState();

  @override
  List<Object?> get props => [];
}

class HomeInitialState extends HomeState {}

class HomeLoadingState extends HomeState {}

class HomeLoadedState extends HomeState {
  final RequestEnumValuesApiModel? requestEnumValues;
  final CurrentSchoolYearApiModel? currentSchoolYear;
  final GuardianApiModel? guardian;
  final List<CertificateApiModel> certificates;
  final List<RequestApiModel> requests;
  final bool isRequestsLoading;
  final RequestStatus? selectedRequestType;
  final RequestStatus? selectedRequestStatus;

  const HomeLoadedState({
    this.requestEnumValues,
    this.currentSchoolYear,
    this.guardian,
    required this.certificates,
    this.requests = const [],
    this.isRequestsLoading = false,
    this.selectedRequestType,
    this.selectedRequestStatus,
  });

  HomeLoadedState copyWith({
    RequestEnumValuesApiModel? requestEnumValues,
    CurrentSchoolYearApiModel? currentSchoolYear,
    GuardianApiModel? guardian,
    List<CertificateApiModel>? certificates,
    List<RequestApiModel>? requests,
    bool? isRequestsLoading,
    RequestStatus? selectedRequestType,
    RequestStatus? selectedRequestStatus,
    bool clearSelectedRequestType = false,
    bool clearSelectedRequestStatus = false,
  }) {
    return HomeLoadedState(
      requestEnumValues: requestEnumValues ?? this.requestEnumValues,
      currentSchoolYear: currentSchoolYear ?? this.currentSchoolYear,
      guardian: guardian ?? this.guardian,
      certificates: certificates ?? this.certificates,
      requests: requests ?? this.requests,
      isRequestsLoading: isRequestsLoading ?? this.isRequestsLoading,
      selectedRequestType:
          clearSelectedRequestType
              ? null
              : selectedRequestType ?? this.selectedRequestType,
      selectedRequestStatus:
          clearSelectedRequestStatus
              ? null
              : selectedRequestStatus ?? this.selectedRequestStatus,
    );
  }

  @override
  List<Object?> get props => [
    requestEnumValues,
    currentSchoolYear,
    guardian,
    certificates,
    requests,
    isRequestsLoading,
    selectedRequestType,
    selectedRequestStatus,
  ];
}

class HomeErrorState extends HomeState {
  final String errorMessage;
  final bool isLocalizationKey;

  const HomeErrorState(this.errorMessage, this.isLocalizationKey);

  @override
  List<Object> get props => [errorMessage, isLocalizationKey];
}
