import 'package:dartz/dartz.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/certificate_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repositories/base_home_repository.dart';

class GetCertificatesUseCase extends BaseUseCase<CertificateResponse, int> {
  final BaseHomeRepository homeRepository;

  GetCertificatesUseCase(this.homeRepository);

  @override
  Future<Either<ErrorApiModel, CertificateResponse>> call(int params) async {
    late Either<ErrorApiModel, CertificateResponse> state;
    await homeRepository
        .getCertificates(params)
        .then((certificatesResponse) {
          state = Right(certificatesResponse);
        })
        .catchError((onError) {
          ErrorApiModel errorApiModel = onError as ErrorApiModel;
          state = Left(errorApiModel);
        });
    return state;
  }
}
