import 'package:dartz/dartz.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/core/usecase.dart';

import 'package:daleel/features/main_taps/screens/home/<USER>/models/certificate_ui_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repositories/base_home_repository.dart';

class GetCertificatesUseCase extends BaseUseCase<CertificateListUiData, int> {
  final BaseHomeRepository homeRepository;

  GetCertificatesUseCase(this.homeRepository);

  @override
  Future<Either<ErrorApiModel, CertificateListUiData>> call(int params) async {
    late Either<ErrorApiModel, CertificateListUiData> state;
    await homeRepository
        .getCertificates(params)
        .then((certificatesResponse) {
          // Convert API response to UI model
          final uiData = CertificateListUiData.fromCertificateResponse(
            certificatesResponse,
          );
          state = Right(uiData);
        })
        .catchError((onError) {
          if (onError is ErrorApiModel) {
            state = Left(onError);
          } else {
            // Handle other types of errors
            state = Left(
              ErrorApiModel(
                message: onError.toString(),
                code: 1015,
                isMessageLocalizationKey: false,
              ),
            );
          }
        });
    return state;
  }
}
