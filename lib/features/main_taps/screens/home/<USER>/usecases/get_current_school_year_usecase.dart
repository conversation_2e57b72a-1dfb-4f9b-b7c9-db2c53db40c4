import 'package:daleel/features/main_taps/screens/home/<USER>/models/current_school_year_api_model.dart';
import 'package:dartz/dartz.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repositories/base_home_repository.dart';

class GetCurrentSchoolYearUseCase
    extends BaseUseCase<CurrentSchoolYearApiModel, NoParams> {
  final BaseHomeRepository homeRepository;

  GetCurrentSchoolYearUseCase(this.homeRepository);

  @override
  Future<Either<ErrorApiModel, CurrentSchoolYearApiModel>> call(
    NoParams params,
  ) async {
    late Either<ErrorApiModel, CurrentSchoolYearApiModel> state;
    await homeRepository
        .getCurrentSchoolYear()
        .then((schoolYearResponse) {
          state = Right(schoolYearResponse);
        })
        .catchError((onError) {
          ErrorApiModel errorApiModel = onError as ErrorApiModel;
          state = Left(errorApiModel);
        });
    return state;
  }
}
