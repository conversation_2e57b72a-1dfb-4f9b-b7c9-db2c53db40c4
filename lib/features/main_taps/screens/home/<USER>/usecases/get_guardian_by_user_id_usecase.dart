import 'package:dartz/dartz.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/guardian_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repositories/base_home_repository.dart';

class GetGuardianByUserIdUseCase
    extends BaseUseCase<GuardianApiModel, NoParams> {
  final BaseHomeRepository homeRepository;

  GetGuardianByUserIdUseCase(this.homeRepository);

  @override
  Future<Either<ErrorApiModel, GuardianApiModel>> call(NoParams params) async {
    late Either<ErrorApiModel, GuardianApiModel> state;
    await homeRepository
        .getGuardianByUserId()
        .then((guardianResponse) {
          state = Right(guardianResponse);
        })
        .catchError((onError) {
          ErrorApiModel errorApiModel = onError as ErrorApiModel;
          state = Left(errorApiModel);
        });
    return state;
  }
}
