import 'package:dartz/dartz.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/models/request_ui_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/repositories/base_home_repository.dart';

class GetMyRequestsUseCase
    extends BaseUseCase<RequestListUiData, RequestSearchModel> {
  final BaseHomeRepository homeRepository;

  GetMyRequestsUseCase(this.homeRepository);

  @override
  Future<Either<ErrorApiModel, RequestListUiData>> call(
    RequestSearchModel params,
  ) async {
    late Either<ErrorApiModel, RequestListUiData> state;
    await homeRepository
        .getMyRequests(params)
        .then((requestsResponse) {
          // Convert API response to UI model
          final uiData = RequestListUiData.fromRequestResponse(
            requestsResponse,
          );
          state = Right(uiData);
        })
        .catchError((onError) {
          ErrorApiModel errorApiModel = onError as ErrorApiModel;
          state = Left(errorApiModel);
        });
    return state;
  }
}
