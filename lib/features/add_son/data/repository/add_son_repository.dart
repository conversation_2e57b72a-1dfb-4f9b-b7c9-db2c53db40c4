import 'package:daleel/features/add_son/data/datasources/add_son_api_manager.dart';
import 'package:daleel/features/add_son/data/datasources/add_son_local_datasources.dart';
import 'package:daleel/features/add_son/data/models/add_son_response_api_model.dart';
import 'package:daleel/features/add_son/data/models/add_son_send_api_model.dart';
import 'package:daleel/features/add_son/domain/repositories/base_add_son_repository.dart';

class AddSonRepository implements BaseAddSonRepository {
  final AddSonLocaleManager addSonLocaleManager;
  final AddSonApiManager addSonApiManager;

  AddSonRepository(this.addSonLocaleManager, this.addSonApiManager);

  @override
  Future<AddSonResponseApiModel> addSon(AddSonSendModel addSonModel) async {
    late AddSonResponseApiModel addSonResponse;
    await addSonApiManager.addSonApi(
      addSonModel,
      (response) {
        addSonResponse = response;
      },
      (errorApiModel) {
        throw errorApiModel;
      },
    );
    return addSonResponse;
  }

  @override
  Future<void> saveSonInfo(AddSonResponseApiModel addSonResponseModel) async {
    await addSonLocaleManager.saveSonInfo(addSonResponseModel);
  }

  @override
  Future<void> clearSonData() async {
    await addSonLocaleManager.clearSonData();
  }
}
