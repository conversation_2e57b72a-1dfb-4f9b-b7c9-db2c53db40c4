import 'package:get_it/get_it.dart';
import 'package:daleel/features/add_son/data/models/add_son_response_api_model.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:daleel/preferences/preferences_manager.dart';

class AddSonLocaleManager {
  final PreferencesManager preferencesManager;

  AddSonLocaleManager(this.preferencesManager);

  final UserMangers userMangers = GetIt.I<UserMangers>();

  Future<void> saveSonInfo(AddSonResponseApiModel addSonResponseModel) async {
    // Save son information locally if needed
    // This can be used for caching or offline functionality
    if (addSonResponseModel.data != null) {
      // Example: Save son data to local storage
      // await preferencesManager.setSonData(addSonResponseModel.data!.toJson());
    }
  }

  Future<void> clearSonData() async {
    // Clear any cached son data
    // await preferencesManager.clearSonData();
  }
}
