import 'package:daleel/apis/_base/dio_api_manager.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/apis/api_urls.dart';
import '../models/add_son_response_api_model.dart';
import '../models/add_son_send_api_model.dart';

class AddSonApiManager {
  final DioApiManager dioApiManager;

  AddSonApiManager(this.dioApiManager);

  Future<void> addSonApi(
    AddSonSendModel sendModel,
    void Function(AddSonResponseApiModel) success,
    void Function(ErrorApiModel) fail,
  ) async {
    await dioApiManager.dio
        .post(ApiUrls.addSon, data: sendModel.toMap())
        .then((response) {
          Map<String, dynamic> extractedData =
              response.data as Map<String, dynamic>;
          AddSonResponseApiModel wrapper = AddSonResponseApiModel.fromJson(
            extractedData,
          );
          success(wrapper);
        })
        .catchError((error) {
          fail(ErrorApiModel.identifyError(error: error));
        });
  }
}
