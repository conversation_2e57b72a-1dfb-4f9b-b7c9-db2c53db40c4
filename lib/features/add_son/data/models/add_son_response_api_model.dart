class AddSonResponseApiModel {
  final bool success;
  final String message;
  final SonData? data;

  AddSonResponseApiModel({
    required this.success,
    required this.message,
    this.data,
  });

  factory AddSonResponseApiModel.fromJson(Map<String, dynamic> json) {
    return AddSonResponseApiModel(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? SonData.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data?.toJson(),
    };
  }
}

class SonData {
  final int id;
  final String fullName;
  final String? idNumber;
  final String nationality;
  final String grade;
  final String? notes;
  final String? photoPath;
  final bool hasId;
  final DateTime createdAt;

  SonData({
    required this.id,
    required this.fullName,
    this.idNumber,
    required this.nationality,
    required this.grade,
    this.notes,
    this.photoPath,
    required this.hasId,
    required this.createdAt,
  });

  factory SonData.fromJson(Map<String, dynamic> json) {
    return SonData(
      id: json['id'] ?? 0,
      fullName: json['fullName'] ?? '',
      idNumber: json['idNumber'],
      nationality: json['nationality'] ?? '',
      grade: json['grade'] ?? '',
      notes: json['notes'],
      photoPath: json['photoPath'],
      hasId: json['hasId'] ?? false,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullName': fullName,
      'idNumber': idNumber,
      'nationality': nationality,
      'grade': grade,
      'notes': notes,
      'photoPath': photoPath,
      'hasId': hasId,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}
