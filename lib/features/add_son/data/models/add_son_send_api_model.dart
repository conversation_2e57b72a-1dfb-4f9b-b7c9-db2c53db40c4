class AddSonSendModel {
  final String fullName;
  final String? idNumber;
  final String nationality;
  final String grade;
  final String? notes;
  final String? photoPath;
  final bool hasId;

  AddSonSendModel({
    required this.fullName,
    this.idNumber,
    required this.nationality,
    required this.grade,
    this.notes,
    this.photoPath,
    required this.hasId,
  });

  Map<String, dynamic> toMap() {
    return {
      "fullName": fullName,
      "idNumber": idNumber,
      "nationality": nationality,
      "grade": grade,
      "notes": notes,
      "photoPath": photoPath,
      "hasId": hasId,
    };
  }

  factory AddSonSendModel.fromMap(Map<String, dynamic> map) {
    return AddSonSendModel(
      fullName: map['fullName'] ?? '',
      idNumber: map['idNumber'],
      nationality: map['nationality'] ?? '',
      grade: map['grade'] ?? '',
      notes: map['notes'],
      photoPath: map['photoPath'],
      hasId: map['hasId'] ?? false,
    );
  }
}
