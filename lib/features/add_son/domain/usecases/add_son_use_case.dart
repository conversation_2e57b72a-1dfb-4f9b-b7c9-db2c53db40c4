import 'package:dartz/dartz.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/core/usecase.dart';
import 'package:daleel/features/add_son/data/models/add_son_response_api_model.dart';
import 'package:daleel/features/add_son/data/models/add_son_send_api_model.dart';
import 'package:daleel/features/add_son/domain/repositories/base_add_son_repository.dart';

class AddSonUseCase
    extends BaseUseCase<AddSonResponseApiModel, AddSonSendModel> {
  BaseAddSonRepository addSonRepository;

  AddSonUseCase(this.addSonRepository);

  @override
  Future<Either<ErrorApiModel, AddSonResponseApiModel>> call(
    AddSonSendModel params,
  ) async {
    late Either<ErrorApiModel, AddSonResponseApiModel> state;
    await addSonRepository
        .addSon(params)
        .then((addSonResponse) {
          state = Right(addSonResponse);
        })
        .catchError((onError) {
          ErrorApiModel errorApiModel = onError as ErrorApiModel;
          state = Left(errorApiModel);
        });
    return state;
  }
}
