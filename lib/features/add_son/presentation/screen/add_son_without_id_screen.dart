import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/extensions/extension_localization.dart';
import '../../../../utils/locale/app_localization_keys.dart';
import '../../../../res/app_colors.dart';
import '../../../../utils/validations/app_validate.dart';
import '../../../widgets/app_buttons/app_elevated_button.dart';
import '../../../widgets/text_field/app_text_form_filed_widget.dart';
import '../../../widgets/text_field/custom_drop_down_form_filed_widget.dart';

import '../../data/models/add_son_send_api_model.dart';
import '../bloc/add_son_bloc.dart';
import '../bloc/add_son_event.dart';
import '../bloc/add_son_state.dart';

class AddSonWithoutIdScreen extends StatefulWidget {
  const AddSonWithoutIdScreen({super.key});

  @override
  State<AddSonWithoutIdScreen> createState() => _AddSonWithoutIdScreenState();
}

class _AddSonWithoutIdScreenState extends State<AddSonWithoutIdScreen>
    with AppValidate {
  final _formKey = GlobalKey<FormState>();
  AutovalidateMode _autoValidateMode = AutovalidateMode.disabled;

  // Controllers
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  // Dropdown values
  CustomDropDownItem? _selectedNationality;
  CustomDropDownItem? _selectedGrade;

  // Sample data for dropdowns
  final List<CustomDropDownItem> _nationalityItems = [
    CustomDropDownItem(key: 1, value: 'Emirati'),
    CustomDropDownItem(key: 2, value: 'Saudi'),
    CustomDropDownItem(key: 3, value: 'Egyptian'),
    CustomDropDownItem(key: 4, value: 'Jordanian'),
    CustomDropDownItem(key: 5, value: 'Lebanese'),
  ];

  final List<CustomDropDownItem> _gradeItems = [
    CustomDropDownItem(key: 1, value: 'Son'),
    CustomDropDownItem(key: 2, value: 'Daughter'),
    CustomDropDownItem(key: 3, value: 'Guardian'),
  ];

  @override
  void dispose() {
    _fullNameController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.I<AddSonBloc>(),
      child: Scaffold(
        backgroundColor: AppColors.scaffoldBackground,
        appBar: AppBar(
          title: Text(context.translate(LocalizationKeys.addNewSonTitle)),
          backgroundColor: AppColors.colorSecondary,
          foregroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.pop(),
          ),
        ),
        body: BlocConsumer<AddSonBloc, AddSonState>(
          listener: (context, state) {
            if (state is AddSonSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.addSonResponse.message),
                  backgroundColor: Colors.green,
                ),
              );
              context.pop();
            } else if (state is AddSonError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.error.message),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          builder: (context, state) {
            return Form(
              key: _formKey,
              autovalidateMode: _autoValidateMode,
              child: SingleChildScrollView(
                padding: EdgeInsets.all(20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 20.h),

                    // Full Name Field
                    _buildTextFieldWithLabel(
                      label: context.translate(
                        LocalizationKeys.fullNameRequired,
                      ),
                      hintText: context.translate(
                        LocalizationKeys.fullNameHint,
                      ),
                      controller: _fullNameController,
                      validator: fullNameValidator,
                      isRequired: true,
                    ),

                    SizedBox(height: 20.h),

                    // Nationality and Grade Row
                    Row(
                      children: [
                        Expanded(
                          child: _buildDropdownWithLabel(
                            label: context.translate(
                              LocalizationKeys.nationalityField,
                            ),
                            hintText: context.translate(
                              LocalizationKeys.nationalityHint,
                            ),
                            items: _nationalityItems,
                            selectedValue: _selectedNationality,
                            onChanged: (value) {
                              setState(() {
                                _selectedNationality = value;
                              });
                            },
                            validator:
                                (value) =>
                                    value == null
                                        ? context.translate(
                                          LocalizationKeys.requiredField,
                                        )
                                        : null,
                            isRequired: true,
                          ),
                        ),
                        SizedBox(width: 15.w),
                        Expanded(
                          child: _buildDropdownWithLabel(
                            label: context.translate(LocalizationKeys.grade),
                            hintText: context.translate(
                              LocalizationKeys.gradeHint,
                            ),
                            items: _gradeItems,
                            selectedValue: _selectedGrade,
                            onChanged: (value) {
                              setState(() {
                                _selectedGrade = value;
                              });
                            },
                            validator:
                                (value) =>
                                    value == null
                                        ? context.translate(
                                          LocalizationKeys.requiredField,
                                        )
                                        : null,
                            isRequired: true,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 30.h),

                    // Required Documents Section
                    Text(
                      context.translate(LocalizationKeys.requiredDocuments),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.colorPrimary,
                      ),
                    ),

                    SizedBox(height: 15.h),

                    // Personal Photo Upload
                    _buildUploadSection(
                      title: context.translate(LocalizationKeys.personalPhoto),
                      subtitle: context.translate(
                        LocalizationKeys.uploadPdfImage,
                      ),
                      isRequired: true,
                    ),

                    SizedBox(height: 30.h),

                    // Notes Field
                    _buildTextFieldWithLabel(
                      label: context.translate(LocalizationKeys.notes),
                      hintText: context.translate(LocalizationKeys.notesHint),
                      controller: _notesController,
                      maxLines: 4,
                      isRequired: false,
                    ),

                    SizedBox(height: 40.h),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: AppElevatedButton.withTitle(
                            title: context.translate(
                              LocalizationKeys.saveButton,
                            ),
                            onPressed:
                                state is AddSonLoading ? null : _handleSave,
                            color: AppColors.colorPrimary,
                          ),
                        ),
                        SizedBox(width: 15.w),
                        Expanded(
                          child: AppElevatedButton.withTitle(
                            title: context.translate(LocalizationKeys.cancel),
                            onPressed: () => context.pop(),
                            color: Colors.transparent,
                            borderColor: AppColors.colorPrimary,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 20.h),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTextFieldWithLabel({
    required String label,
    required String hintText,
    required TextEditingController controller,
    String? Function(String?)? validator,
    bool isRequired = false,
    int maxLines = 1,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.buttonBlackTextColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            if (isRequired)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.formFieldAsterisk,
                  fontWeight: FontWeight.w400,
                ),
              ),
          ],
        ),
        const SizedBox(height: 10),
        AppTextFormField(
          hintText: hintText,
          fillColor: Colors.transparent,
          hintTextStyle: TextStyle(
            fontSize: 16,
            color: AppColors.formFieldHintText,
            fontWeight: FontWeight.w400,
          ),
          controller: controller,
          validator: validator,
          enableBorderColor: AppColors.formFieldBorder,
          maxLines: maxLines,
          textInputType: keyboardType,
        ),
      ],
    );
  }

  Widget _buildDropdownWithLabel({
    required String label,
    required String hintText,
    required List<CustomDropDownItem> items,
    required CustomDropDownItem? selectedValue,
    required ValueChanged<CustomDropDownItem?> onChanged,
    required String? Function(CustomDropDownItem?)? validator,
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.buttonBlackTextColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            if (isRequired)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.formFieldAsterisk,
                  fontWeight: FontWeight.w400,
                ),
              ),
          ],
        ),
        const SizedBox(height: 10),
        CustomDropDownFormFiledWidget(
          hintText: hintText,
          items: items,
          selectedValue: selectedValue,
          onChanged: onChanged,
          validator: validator,
          fillColor: Colors.transparent,
          enableBorderColor: AppColors.formFieldBorder,
          hintTextStyle: TextStyle(
            fontSize: 16,
            color: AppColors.formFieldHintText,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Widget _buildUploadSection({
    required String title,
    required String subtitle,
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.buttonBlackTextColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            if (isRequired)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.formFieldAsterisk,
                  fontWeight: FontWeight.w400,
                ),
              ),
          ],
        ),
        SizedBox(height: 10.h),
        GestureDetector(
          onTap: () {
            // TODO: Implement file upload functionality
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Upload functionality will be implemented'),
              ),
            );
          },
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 15.w),
            decoration: BoxDecoration(
              color: Colors.transparent,
              border: Border.all(color: AppColors.formFieldBorder, width: 2),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.upload_file,
                  color: AppColors.colorPrimary,
                  size: 24.w,
                ),
                SizedBox(width: 10.w),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.formFieldHintText,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _handleSave() {
    setState(() {
      _autoValidateMode = AutovalidateMode.always;
    });

    if (_formKey.currentState?.validate() ?? false) {
      final addSonModel = AddSonSendModel(
        fullName: _fullNameController.text.trim(),
        nationality: _selectedNationality?.value ?? '',
        grade: _selectedGrade?.value ?? '',
        notes: _notesController.text.trim(),
        hasId: false,
      );

      context.read<AddSonBloc>().add(AddSonSubmitEvent(addSonModel));
    }
  }
}
