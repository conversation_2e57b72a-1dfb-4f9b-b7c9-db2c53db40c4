import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/extensions/extension_localization.dart';
import '../../../../utils/locale/app_localization_keys.dart';
import '../../../../res/app_colors.dart';

class AddSonSelectionScreen extends StatelessWidget {
  const AddSonSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: Text(context.translate(LocalizationKeys.addNewSonTitle)),
        backgroundColor: AppColors.colorSecondary,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          children: [
            SizedBox(height: 40.h),
            
            // Son with ID option
            _buildOptionCard(
              context: context,
              title: context.translate(LocalizationKeys.sonWithId),
              description: context.translate(LocalizationKeys.sonWithIdDescription),
              onTap: () {
                context.push('/add-son-with-id');
              },
            ),
            
            SizedBox(height: 20.h),
            
            // Son without ID option
            _buildOptionCard(
              context: context,
              title: context.translate(LocalizationKeys.sonWithoutId),
              description: context.translate(LocalizationKeys.sonWithoutIdDescription),
              onTap: () {
                context.push('/add-son-without-id');
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionCard({
    required BuildContext context,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.colorPrimary,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.subTitleGray,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppColors.colorPrimary,
              size: 20.w,
            ),
          ],
        ),
      ),
    );
  }
}
