import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:daleel/features/add_son/domain/usecases/add_son_use_case.dart';
import 'add_son_event.dart';
import 'add_son_state.dart';

class AddSonBloc extends Bloc<AddSonEvent, AddSonState> {
  final AddSonUseCase addSonUseCase;

  AddSonBloc(this.addSonUseCase) : super(AddSonInitial()) {
    on<AddSonSubmitEvent>(_onAddSonSubmit);
    on<AddSonResetEvent>(_onAddSonReset);
  }

  Future<void> _onAddSonSubmit(
    AddSonSubmitEvent event,
    Emitter<AddSonState> emit,
  ) async {
    emit(AddSonLoading());

    final result = await addSonUseCase(event.addSonModel);

    result.fold(
      (error) => emit(AddSonError(error)),
      (response) => emit(AddSonSuccess(response)),
    );
  }

  void _onAddSonReset(
    AddSonResetEvent event,
    Emitter<AddSonState> emit,
  ) {
    emit(AddSonInitial());
  }
}
