import 'package:equatable/equatable.dart';
import 'package:daleel/features/add_son/data/models/add_son_send_api_model.dart';

abstract class AddSonEvent extends Equatable {
  const AddSonEvent();

  @override
  List<Object> get props => [];
}

class AddSonSubmitEvent extends AddSonEvent {
  final AddSonSendModel addSonModel;

  const AddSonSubmitEvent(this.addSonModel);

  @override
  List<Object> get props => [addSonModel];
}

class AddSonResetEvent extends AddSonEvent {
  const AddSonResetEvent();
}
