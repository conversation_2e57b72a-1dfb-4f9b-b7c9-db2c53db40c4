import 'package:equatable/equatable.dart';
import 'package:daleel/apis/errors/error_api_model.dart';
import 'package:daleel/features/add_son/data/models/add_son_response_api_model.dart';

abstract class AddSonState extends Equatable {
  const AddSonState();

  @override
  List<Object> get props => [];
}

class AddSonInitial extends AddSonState {}

class AddSonLoading extends AddSonState {}

class AddSonSuccess extends AddSonState {
  final AddSonResponseApiModel addSonResponse;

  const AddSonSuccess(this.addSonResponse);

  @override
  List<Object> get props => [addSonResponse];
}

class AddSonError extends AddSonState {
  final ErrorApiModel error;

  const AddSonError(this.error);

  @override
  List<Object> get props => [error];
}
