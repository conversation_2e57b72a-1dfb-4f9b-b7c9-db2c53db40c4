class ApiUrls {
  /// URLs
  static get baseUrl => baseDevUrl;
  static const baseDevUrl =
      "https://daleel-spea-back-dfhscngmcjemhee9.eastus-01.azurewebsites.net";
  static const baseQcUrl =
      "https://daleel-spea-back-qa-g4ftenfwdrg3acf7.eastus-01.azurewebsites.net";
  static const baseProductionUrl =
      "https://daleel-spea-back-dfhscngmcjemhee9.eastus-01.azurewebsites.net";

  static const login = '/api/User/Auth/Login';
  static const currentSchoolYear = '/api/SchoolYear/current';
  static const requestEnumValues = '/api/StudentDashboard/GetRequestEnumValues';
  static const certificates = '/api/Certificate/certificate-requestsByYearId';
  static const myRequests = '/api/Guardian/my-requests-listByYearId';
  static const guardianByUserId = '/api/Guardian/guardian-by-user-id';
  static const addSon = '/api/Guardian/add-son';
}
