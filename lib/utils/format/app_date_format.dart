import 'package:intl/intl.dart';

class AppDateFormat {
  static DateFormat appDateFormat(String local) {
    return DateFormat.yMMMd(local);
  }

  static DateFormat appDatePickerFormat(String local) {
    return DateFormat.yMMMMd(local);
  }

  static DateTime appDatePickerParse(String date, String local) {
    return appDatePickerFormat(local).parse(date);
  }

  static String formattingDatePicker(DateTime date, String local) {
    return appDatePickerFormat(local).format(date);
  }

  static String formattingDate(DateTime date, String local) {
    return appDateFormat(local).format(date);
  }

  static String formattingOnlyMonthDay(DateTime date, String local) {
    return DateFormat.MMMd(local).format(date);
  }

  static String formattingDateToMonth(DateTime date, String local) {
    return DateFormat.yMMMM(local).format(date);
  }

  static String formattingMonthDay(DateTime date, String local) {
    return DateFormat.yMMMd(local).format(date);
  }

  static String formattingApiDate(DateTime date, String local) {
    return DateFormat("yyyy-MM-dd", local).format(date);
  }

  static DateTime? formattingApiDateFromString(String? date) {
    try {
      // 10/25/2023
      return DateFormat('dd/MM/yyyy').parse(date!);
    } catch (e) {
      return null;
    }
  }

  static String formattingDisplayDate(DateTime date, String local) {
    return DateFormat("MM/dd/yyyy", local).format(date);
  }

  static String formattingMailboxDetailsDisplayDate(
      DateTime date, String local) {
    return DateFormat("MMM-dd-yyyy", local).format(date);
  }

  static String formattingInvitationDisplayDate(DateTime date, String local) {
    return DateFormat("dd/MM/yyyy", local).format(date);
  }

  static String formattingDisplayTime(DateTime date, String local) {
    return DateFormat("hh:mm a", local).format(date);
  }

  static String formattingChooseTime(DateTime date, String local) {
    return DateFormat("HH:mm", local).format(date);
  }

  static String corporateProfileBDFormat(String dateStr, String locale) {
    try {
      final date = DateFormat('MMM dd, yyyy', locale).parse(dateStr);
      return DateFormat("dd/MM/yyyy").format(date);
    } catch (e) {
      return "";
    }
  }

  static String tryFormattingDate(DateTime? date, String local) {
    try {
      return appDateFormat(local).format(date!);
    } catch (e) {
      return "";
    }
  }

  static String formattingTime(DateTime date, String local) {
    return DateFormat.jm(local).format(date);
  }

  static String formattingDateTime(DateTime date, String local) {
    return appDateFormat(local).add_jm().format(date);
  }

  static String formattingDateTimeUtc(DateTime date, String local) {
    return DateFormat('d-M-yyyy. hh:mm a', local).format(date.toUtc());
  }

  static String formattingDateTTimeZ(DateTime date, String local) {
    return DateFormat('yyyy-MM-ddThh:mm:ss', local).format(date);
  }

  static String formatDayMonthAndYearWithTime(DateTime date, String local) {
    return DateFormat('EEEE, d MMMM yyyy. hh:mm a', local).format(date);
  }

  static String formatDateWithTime(DateTime date, String local) {
    return DateFormat('MMMM dd, yyyy - hh:mm a', local).format(date);
  }

  static String formattingDayMonthTime(DateTime date, String local) {
    return DateFormat('EEEE, d MMMM, ', local).format(date) +
        formattingTime(date, local);
  }

  static String formattingDisplayDateTime(DateTime date, String local) {
    return DateFormat("MM/dd/yyyy hh:mm", local).format(date.toLocal());
  }

  static String formatIsoToBirthDate(String isoStr, String locale) {
    try {
      final date = DateTime.parse(isoStr);
      return DateFormat('dd/MM/yyyy', locale).format(date);
    } catch (e) {
      return "";
    }
  }

  static String formatBirthDateToIso(String dateStr, String locale) {
    try {
      final date = DateFormat('dd/MM/yyyy', locale).parse(dateStr);
      return date.toIso8601String();
    } catch (e) {
      return "";
    }
  }

  static DateTime? parseIsoDateTime(String isoStr) {
    try {
      return DateTime.parse(isoStr);
    } catch (e) {
      return null;
    }
  }

  static String formatDateStringToFormat(
      String dateStr, String outputFormat, String locale) {
    try {
      final date = DateTime.parse(dateStr).toLocal();
      return DateFormat(outputFormat, locale).format(date);
    } catch (e) {
      return "";
    }
  }


}
